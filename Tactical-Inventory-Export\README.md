# 🚀 Tactical Inventory Export - Script Completo

Script único e auto-contido para geração de inventário Tactical RMM com anexo Excel, otimizado para execução no servidor Tactical RMM (Ubuntu Server).

## ⚡ Início Rápido

### 1. <PERSON><PERSON> Script no Tactical RMM
1. Acesse **Setting<PERSON> → Script Manager → Add Script**
2. Configure:
   - **Name:** `Inventário Completo com Excel`
   - **Script Type:** `PowerShell`
   - **Timeout:** `600` segundos
3. Cole o conteúdo completo do arquivo `Tactical-Inventory-Complete.ps1`

### 2. Configurar Argumentos
```bash
# Argumentos básicos
-ClientId 8 -EmailTo "<EMAIL>" -SMTPUser "<EMAIL>" -SMTPPassword "sua_senha"

# Argumentos avançados
-ClientId 8 -EmailTo "<EMAIL>,<EMAIL>" -SMTPUser "<EMAIL>" -SMTPPassword "sua_senha" -EmailSubject "Relatório Semanal" -IncludeOffline false
```

### 3. Executar
- **Manual:** Agents → Tasks → Run Script
- **Automático:** Automation Manager → Add Task → Schedule

## 📁 Arquivos do Projeto

```
Tactical-Inventory-Export/
├── 📄 README.md                           # Este arquivo
├── 🚀 Tactical-Inventory-Complete.ps1     # Script único completo (PRINCIPAL)
├── 📄 README-Complete-Script.md           # Documentação detalhada do script
└── 📄 TACTICAL-SETUP-EXAMPLES.md          # Exemplos práticos de configuração
```

## 🚀 Script Principal: Tactical-Inventory-Complete.ps1

### ✨ **Características**
- ✅ **Tudo em um arquivo** - não precisa de dependências externas
- ✅ **Auto-instalação** do módulo ImportExcel se necessário
- ✅ **Verificação automática** de pré-requisitos
- ✅ **Anexo Excel** com múltiplas abas por site
- ✅ **Email HTML profissional** com estatísticas visuais
- ✅ **Limpeza automática** de arquivos temporários
- ✅ **Logs detalhados** com cores para acompanhamento

### 📋 **Parâmetros**

#### **Obrigatórios:**
- `ClientId` - ID do cliente no Tactical RMM
- `EmailTo` - Email(s) de destino (separados por vírgula)
- `SMTPUser` - Usuário SMTP
- `SMTPPassword` - Senha SMTP

#### **Opcionais:**
- `EmailSubject` - Assunto personalizado
- `SMTPServer` - Servidor SMTP (padrão: smtp.gmail.com)
- `SMTPPort` - Porta SMTP (padrão: 587)
- `IncludeOffline` - Incluir offline (padrão: true)

### 💡 **Exemplos de Uso**

#### **Básico:**
```bash
-ClientId 8 -EmailTo "<EMAIL>" -SMTPUser "<EMAIL>" -SMTPPassword "senha123"
```

#### **Avançado:**
```bash
-ClientId 8 -EmailTo "<EMAIL>,<EMAIL>" -SMTPUser "<EMAIL>" -SMTPPassword "senha123" -EmailSubject "Relatório Semanal" -IncludeOffline false
```

## 📊 **O que o Script Gera**

### 📧 **Email HTML Profissional**
- Design moderno com gradientes e cores
- Cards estatísticos visuais (Total, Online, Offline, Sites)
- Tabelas organizadas por site
- Informações do servidor Ubuntu
- Nota destacada sobre anexo Excel

### 📎 **Anexo Excel Automático**
- **Múltiplas abas** (uma para cada site)
- **Aba "Resumo_Geral"** com estatísticas consolidadas
- **Formatação automática** (AutoSize, AutoFilter, FreezeTopRow)
- **Dados completos** de cada estação
- **Nome com timestamp** para organização

### 📋 **Dados Incluídos**
- ID do Agente, Hostname, Cliente, Site
- Status de conectividade com timestamp
- Sistema operacional e arquitetura
- Informações de hardware (CPU, RAM)
- IP público e versão do agente
- Usuário logado e domínio
- Falhas de serviços e checks
- Modo de manutenção e observações

## 🔧 **Configurações SMTP**

### **Gmail/Google Workspace:**
```bash
-SMTPServer "smtp.gmail.com" -SMTPPort 587 -SMTPUser "<EMAIL>" -SMTPPassword "senha_de_aplicativo"
```

### **Office 365/Outlook:**
```bash
-SMTPServer "smtp.office365.com" -SMTPPort 587 -SMTPUser "<EMAIL>" -SMTPPassword "sua_senha"
```

## 📖 **Documentação Adicional**

- **README-Complete-Script.md** - Documentação detalhada do script
- **TACTICAL-SETUP-EXAMPLES.md** - Exemplos práticos de configuração

---

**Desenvolvido por NVirtual** 🚀
