<#
.SYNOPSIS
    Atualiza automaticamente o site de um agente Tactical RMM baseado no IP local

.DESCRIPTION
    Este script detecta o IP local da máquina na faixa 192.168.* e atualiza
    automaticamente o site do agente no Tactical RMM baseado no mapeamento
    configurado de faixas de IP para sites.

.PARAMETER ApiToken
    Token de API do Tactical RMM. Se não fornecido, usa a variável de ambiente
    TACTICAL_RMM_TOKEN ou o token padrão configurado no script.

.PARAMETER VerboseOutput
    Ativa saída detalhada para debugging

.PARAMETER WhatIf
    Mostra o que seria feito sem executar as alterações

.EXAMPLE
    .\Set-TacticalSite-Cliente.ps1
    Executa o script com configurações padrão

.EXAMPLE
    .\Set-TacticalSite-Cliente.ps1 -ApiToken "SEU_TOKEN" -VerboseOutput
    Executa com token específico e saída detalhada

.EXAMPLE
    .\Set-TacticalSite-Cliente.ps1 -WhatIf
    Simula a execução sem fazer alterações

.NOTES
    Versão: 2.1
    Autor: NVirtual
    Data: 2025-06-27

    Requisitos:
    * PowerShell 2.0 ou superior
    * Acesso à API do Tactical RMM
    * Token com permissões de leitura/escrita de agentes
#>

# Token de API do Tactical RMM (gerado via interface web, atenção ao escopo)
# Recomenda-se usar variável de ambiente ou parâmetro para maior segurança
param(
    [string]$ApiToken = $env:TACTICAL_RMM_TOKEN,
    [switch]$VerboseOutput,
    [switch]$WhatIf
)

# ============================================================================
# CONFIGURAÇÕES E INICIALIZAÇÃO
# ============================================================================

# CONFIGURAÇÕES DO CLIENTE
$apiUrl = "https://api.centralmesh.nvirtual.com.br"
$clientesPermitidos = @("NVirtual Info", "Sumire")  # Clientes permitidos

# Mapeamento de nomes para IDs (baseado na estrutura da API)
$clienteIds = @{
    "Sumire" = 8
    "NVirtual Info" = 1  # Adicione o ID correto se necessário
}

$siteIds = @{
    "CD - Sorocaba" = 40
    "Loja 01 - Avare" = 12
    "Loja 02 - Avare" = 13
    "Loja 03 - Marilia" = 14
    "Loja 04 - Aracatuba" = 15
    "Loja 05 - Presidente Prudente" = 16
    "Loja 07 - Marilia" = 33
    "Loja 08 - Bauru" = 27
    "Loja 09 - Assis" = 24
    "Loja 10 - Andradina" = 22
    "Loja 11 - Birigui" = 11
    "Loja 12 - Itapeva" = 31
    "Loja 13 - Bauru" = 26
    "Loja 14 - Ourinhos" = 35
    "Loja 16 - Sorocaba" = 42
    "Loja 25 - Santa Cruz do Sul" = 17
    "Loja 26 - Barao" = 23
    "Loja 30 - Aricanduva" = 30
    "Loja 32 - Pinheiros" = 34
    "Loja 33 - Itaim" = 37
    "Loja 55 - Botucatu" = 28
    "Loja 56 - Botucatu" = 28
    "Loja 57 - Jau" = 32
    "Loja 60 - Sao Carlos" = 19
    "Loja 61 - Vila Prado - Sao Carlos" = 21
    "Loja 62 - Shopping Sao Carlos" = 20
    "Loja 63 - Shopping Ribeirao Preto" = 38
    "Loja 68 - Marilia" = 33
    "Loja 70 - Votorantin" = 41
    "Loja 71 - Porto Ferreira" = 36
    "Loja 72 - Shopping Braganca" = 29
    "Matriz" = 54
}

# Mapeamento de faixa de IP para ID do site
$sites = @{
    "192.168.0." = 54      # Matriz
    "192.168.15." = 40     # CD Sorocaba
    "192.168.101." = 12    # Avare
    "192.168.102." = 13   # Avare
    "192.168.103." = 14   # Marilia
    "192.168.104." = 15   # Aracatuba
    "192.168.105." = 16    # Presidente prudente
    "192.168.107." = 33    # Marilia
    "192.168.108." = 27    # Bauru
    "192.168.109." = 24    # Assis
    "192.168.110." = 22   # Andradina
    "192.168.111." = 11   # Birigui
    "192.168.112." = 31   # Itapeva
    "192.168.113." = 26   # Bauru
    "192.168.114." = 35   # Ourinhos
    "192.168.155." = 17   # Botucatu
    "192.168.156." = 28   # Botucatu
    "192.168.157." = 32   # Jau
    "192.168.160." = 19   # Sao Carlos
    "192.168.161." = 21   # Vila prado - Sao Carlos
    "192.168.162." = 20   # Sao carlos Shopping
    "192.168.163." = 38   # Ribeirao Preto 63
    "192.168.168." = 34   # Marilia 68
    "192.168.175." = 41   # Votorantim 70
    "192.168.171." = 36   # Porto Ferreira
    "192.168.172." = 29   # Braganca Shoppipng
  


}

# Configurar verbosidade
if ($VerboseOutput) {
    $VerbosePreference = "Continue"
} else {
    # Ativar verbose automaticamente se houver problemas de conectividade
    $VerbosePreference = "SilentlyContinue"
}

# Configurar TLS 1.2 para compatibilidade com APIs modernas
try {
    # Forçar TLS 1.2 para PowerShell antigo
    [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.SecurityProtocolType]::Tls12
    Write-Verbose "TLS 1.2 configurado com sucesso"
} catch {
    try {
        # Fallback para versões muito antigas - tentar TLS 1.1 e 1.2
        [System.Net.ServicePointManager]::SecurityProtocol = 3072 -bor 768  # TLS 1.2 + TLS 1.1
        Write-Verbose "TLS 1.1/1.2 configurado via fallback"
    } catch {
        Write-Verbose "Não foi possível configurar TLS - usando configuração padrão"
    }
}

# Configurar validação de certificado mais permissiva para ambientes corporativos
try {
    Add-Type @"
        using System.Net;
        using System.Security.Cryptography.X509Certificates;
        public class TrustAllCertsPolicy : ICertificatePolicy {
            public bool CheckValidationResult(ServicePoint srvPoint, X509Certificate certificate, WebRequest request, int certificateProblem) {
                return true;
            }
        }
"@
    [System.Net.ServicePointManager]::CertificatePolicy = New-Object TrustAllCertsPolicy
    Write-Verbose "Política de certificado permissiva configurada"
} catch {
    Write-Verbose "Não foi possível configurar política de certificado personalizada"
}

if (-not $ApiToken) {
    $ApiToken = "N4TXS3T3FUUJTXZYSV6AQ5X9TOZPWHE8"  # Fallback - substitua por seu token
    Write-Verbose "Usando token padrão configurado no script"
} else {
    Write-Verbose "Usando token da variável de ambiente ou parâmetro"
}

# Função para pegar o IP local na faixa 192.168.*
function Get-LocalIP {
    Write-Verbose "Detectando IP local na faixa 192.168.*"
    
    $ipAddresses = Get-NetIPAddress -AddressFamily IPv4 | Where-Object {
        $_.IPAddress -like "192.168.*" -and
        $_.IPAddress -notlike "169.254.*" -and
        $_.IPAddress -notlike "127.*" -and
        ($_.PrefixOrigin -eq "Manual" -or $_.PrefixOrigin -eq "Dhcp") -and
        $_.AddressState -eq "Preferred"
    }

    if ($ipAddresses) {
        $selectedIP = $ipAddresses[0].IPAddress
        Write-Verbose "IP selecionado: $selectedIP"
        return $selectedIP
    }
    
    Write-Verbose "Nenhum IP válido encontrado na faixa 192.168.*"
    return $null
}

# Função para validar conectividade com a API (compatível com PowerShell 2.0+)
function Test-TacticalAPI {
    param([string]$ApiUrl, [string]$Token)

    try {
        Write-Verbose "Testando conectividade com a API: $ApiUrl"
        Write-Verbose "Token usado: $($Token.Substring(0, 8))..."

        # Verificar se Invoke-RestMethod está disponível
        if (Get-Command "Invoke-RestMethod" -ErrorAction SilentlyContinue) {
            Write-Verbose "Usando Invoke-RestMethod"
            $null = Invoke-RestMethod -Uri "$ApiUrl/agents/" -Headers @{
                "X-API-KEY" = $Token
            } -Method GET -TimeoutSec 30
        } else {
            Write-Verbose "Usando WebClient"
            # Fallback para PowerShell 2.0
            $webClient = New-Object System.Net.WebClient
            $webClient.Headers.Add("X-API-KEY", $Token)
            $webClient.Headers.Add("Content-Type", "application/json")
            $webClient.Headers.Add("User-Agent", "PowerShell-TacticalRMM-Script/1.0")

            # Configurar timeout (se suportado)
            try {
                $webClient.Timeout = 30000  # 30 segundos
            } catch {
                Write-Verbose "Timeout não suportado nesta versão do WebClient"
            }

            $url = "$ApiUrl/agents/"
            Write-Verbose "Fazendo requisição para: $url"

            $response = $webClient.DownloadString($url)
            $webClient.Dispose()
        }

        Write-Verbose "API respondeu com sucesso"
        return $true
    } catch {
        Write-Host "Erro detalhado ao testar API:"
        Write-Host "   Mensagem: $($_.Exception.Message)"
        if ($_.Exception.Response) {
            Write-Host "   Status Code: $($_.Exception.Response.StatusCode)"
            Write-Host "   Status Description: $($_.Exception.Response.StatusDescription)"
        }
        if ($_.Exception.InnerException) {
            Write-Host "   Erro interno: $($_.Exception.InnerException.Message)"
        }
        Write-Verbose "Erro completo: $($_.Exception | Out-String)"
        return $false
    }
}

# Validar conectividade com a API antes de prosseguir
Write-Host "Testando conectividade com a API..."

# Teste básico de conectividade de rede
try {
    Write-Verbose "Testando conectividade básica com o servidor..."
    $uri = [System.Uri]$apiUrl
    $tcpClient = New-Object System.Net.Sockets.TcpClient
    $tcpClient.Connect($uri.Host, $uri.Port)
    $tcpClient.Close()
    Write-Verbose "Conectividade TCP OK"
} catch {
    Write-Host "Erro de conectividade de rede:"
    Write-Host "   Não foi possível conectar com $($uri.Host):$($uri.Port)"
    Write-Host "   Erro: $($_.Exception.Message)"
    Write-Host "   Verifique firewall, proxy ou conectividade de rede"
    exit 1
}

if (-not (Test-TacticalAPI -ApiUrl $apiUrl -Token $ApiToken)) {
    Write-Host "Não foi possível conectar com a API do Tactical RMM"
    Write-Host "   Verifique:"
    Write-Host "   - URL da API: $apiUrl"
    Write-Host "   - Token de autenticação"
    Write-Host "   - Conectividade de rede"
    exit 1
}

# Obter nome da máquina local
$hostname = $env:COMPUTERNAME
Write-Host "Hostname local: $hostname"

# Buscar agente pelo hostname na API
Write-Verbose "Buscando agente com hostname: $hostname"
try {
    # Verificar se Invoke-RestMethod está disponível (PowerShell 3.0+)
    if (Get-Command "Invoke-RestMethod" -ErrorAction SilentlyContinue) {
        Write-Verbose "Usando Invoke-RestMethod (PowerShell 3.0+)"
        $response = Invoke-RestMethod -Uri "$apiUrl/agents/" -Headers @{
            "X-API-KEY" = $ApiToken
        } -TimeoutSec 30
    } else {
        Write-Verbose "Usando WebClient (PowerShell 2.0)"
        # Fallback para PowerShell 2.0
        $webClient = New-Object System.Net.WebClient
        $webClient.Headers.Add("X-API-KEY", $ApiToken)
        $webClient.Headers.Add("Content-Type", "application/json")
        $webClient.Headers.Add("User-Agent", "PowerShell-TacticalRMM-Script/1.0")

        # Configurar timeout (se suportado)
        try {
            $webClient.Timeout = 30000  # 30 segundos
        } catch {
            Write-Verbose "Timeout não suportado nesta versão do WebClient"
        }

        $responseJson = $webClient.DownloadString("$apiUrl/agents/")
        $webClient.Dispose()

        # Usar ConvertFrom-Json se disponível, senão usar parser manual
        if (Get-Command "ConvertFrom-Json" -ErrorAction SilentlyContinue) {
            $response = $responseJson | ConvertFrom-Json
        } else {
            # Parser JSON manual muito básico para PowerShell 2.0
            Add-Type -AssemblyName System.Web.Extensions
            $jsonSerializer = New-Object System.Web.Script.Serialization.JavaScriptSerializer
            $jsonSerializer.MaxJsonLength = 104857600  # 100MB
            $response = $jsonSerializer.DeserializeObject($responseJson)
        }
    }
} catch {
    Write-Host "Erro ao buscar agente na API: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        Write-Host "   Status Code: $($_.Exception.Response.StatusCode)"
    }
    exit 1
}

# Filtrar agentes pelo hostname local
Write-Verbose "Filtrando agentes pelo hostname: $hostname"
$matchingAgents = $response | Where-Object { $_.hostname -eq $hostname }

Write-Verbose "Agentes encontrados com hostname '$hostname': $($matchingAgents.Count)"

if ($matchingAgents.Count -eq 0) {
    Write-Host "Nenhum agente encontrado com hostname '$hostname'."
    Write-Host "Hostnames disponíveis na API:"
    $response | ForEach-Object { Write-Host "  - $($_.hostname)" } | Select-Object -First 10
    if ($response.Count -gt 10) {
        Write-Host "  ... e mais $($response.Count - 10) agentes"
    }
    exit 1
}

if ($matchingAgents.Count -gt 1) {
    Write-Host "Múltiplos agentes encontrados ($($matchingAgents.Count)) com hostname '$hostname'. Detalhes:"
    for ($i = 0; $i -lt $matchingAgents.Count; $i++) {
        $ag = $matchingAgents[$i]
        Write-Host "  Agente $($i+1): ID=$($ag.agent_id), Hostname=$($ag.hostname), Cliente=$($ag.client_name), Site=$($ag.site_name)"
    }
    Write-Host "Abortando por segurança."
    exit 1
}

$agents = $matchingAgents

# Obter dados do agente
$agent = $agents[0]

$agentId = $agent.agent_id
$clientName = $agent.client_name
$agentSiteAtual = $agent.site_name

# Mapear nomes para IDs
$clientId = $clienteIds[$clientName]
$agentSiteId = $siteIds[$agentSiteAtual]

Write-Host "Agente encontrado. ID: $agentId | Cliente: $clientName (ID: $clientId) | Site atual: $agentSiteAtual (ID: $agentSiteId)"

# Verificar se pertence a um dos clientes permitidos
if (-not ($clientesPermitidos -contains $clientName)) {
    Write-Host "Este agente não pertence a nenhum dos clientes permitidos: $($clientesPermitidos -join ', ')"
    Write-Host "Cliente atual: '$clientName' (ID: $clientId)"
    exit 1
}

# Tentar pegar IP local válido (uso interno para redes 192.168.*)
$ipLocal = Get-LocalIP

if (-not $ipLocal) {
    Write-Host "Nenhum IP local na faixa 192.168.* detectado."
    Write-Host "IPs disponíveis:"
    Get-NetIPAddress -AddressFamily IPv4 | Where-Object { $_.IPAddress -notlike "127.*" } | ForEach-Object {
        Write-Host "   - $($_.IPAddress)"
    }
    exit 1
}

Write-Host "IP detectado: $ipLocal"

# Verificar a qual site ele pertence
$siteIdEsperado = $null
foreach ($prefix in $sites.Keys) {
    if ($ipLocal.StartsWith($prefix)) {
        $siteIdEsperado = $sites[$prefix]
        break
    }
}

if (-not $siteIdEsperado) {
    Write-Host "Faixa de IP não corresponde a nenhum site configurado."
    Write-Host "Atribuindo ao Site ID padrão 113 para IPs não mapeados."
    Write-Host "Sites configurados:"
    foreach ($prefix in $sites.Keys) {
        Write-Host "   - $prefix -> Site ID: $($sites[$prefix])"
    }
    Write-Host "   - IP não mapeado ($ipLocal) -> Site ID: 113 (padrão)"
    $siteIdEsperado = 113
}

Write-Host "Site esperado detectado: ID $siteIdEsperado"

# Atualizar site se for diferente (usando -ne para "not equal" em PowerShell)
if ($agentSiteId -ne $siteIdEsperado) {
    Write-Host "Site atual ID $agentSiteId ($agentSiteAtual) diferente do esperado ID $siteIdEsperado."

    if ($WhatIf) {
        Write-Host "[WHAT-IF] Seria executada a atualização do site para ID $siteIdEsperado"
        Write-Host "   Agente ID: $agentId"
        Write-Host "   Site atual: ID $agentSiteId ($agentSiteAtual) -> Site esperado: ID $siteIdEsperado"
    } else {
        # Criar JSON manualmente para compatibilidade com PowerShell 2.0
        $body = "{`"site`": $siteIdEsperado}"

        Write-Verbose "Enviando requisição PATCH para atualizar site"
        Write-Verbose "Body: $body"

        # Tentar diferentes métodos HTTP para atualização
        $methods = @("PUT", "POST", "PATCH")
        $success = $false

        foreach ($method in $methods) {
            try {
                Write-Verbose "Tentando método $method para atualizar agente"

                # Verificar se Invoke-RestMethod está disponível
                if (Get-Command "Invoke-RestMethod" -ErrorAction SilentlyContinue) {
                    Write-Verbose "Usando Invoke-RestMethod"
                    $updateResult = Invoke-RestMethod -Method $method -Uri "$apiUrl/agents/$agentId/" -Headers @{
                        "X-API-KEY" = $ApiToken
                        "Content-Type" = "application/json"
                    } -Body $body -TimeoutSec 30
                } else {
                    Write-Verbose "Usando WebClient"
                    # Fallback para PowerShell 2.0
                    $webClient = New-Object System.Net.WebClient
                    $webClient.Headers.Add("X-API-KEY", $ApiToken)
                    $webClient.Headers.Add("Content-Type", "application/json")

                    $url = "$apiUrl/agents/$agentId/"

                    if ($method -eq "PUT") {
                        $updateResponse = $webClient.UploadString($url, "PUT", $body)
                    } elseif ($method -eq "POST") {
                        $updateResponse = $webClient.UploadString($url, "POST", $body)
                    } elseif ($method -eq "PATCH") {
                        # PATCH não é suportado diretamente no WebClient, vamos tentar PUT
                        $updateResponse = $webClient.UploadString($url, "PUT", $body)
                    }

                    $webClient.Dispose()
                }

                Write-Host "Site atualizado com sucesso para ID $siteIdEsperado usando método $method."
                Write-Host "Agente ID: $agentId"

                # Log da operação
                $logEntry = "$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss') - Agente $agentId ($hostname) movido para site ID $siteIdEsperado"
                Write-Verbose $logEntry

                $success = $true
                break

            } catch {
                Write-Verbose "Método $method falhou: $($_.Exception.Message)"
                if ($_.Exception.Response.StatusCode -eq 405) {
                    Write-Verbose "Método $method não permitido, tentando próximo..."
                    continue
                } else {
                    # Outro tipo de erro, não relacionado ao método
                    Write-Host "Erro ao atualizar site com método ${method}: $($_.Exception.Message)"
                    if ($_.Exception.Response) {
                        Write-Host "   Status Code: $($_.Exception.Response.StatusCode)"
                        if ($_.Exception.Response.StatusCode -eq 403) {
                            Write-Host "   Possível problema: Token sem permissões suficientes"
                        } elseif ($_.Exception.Response.StatusCode -eq 404) {
                            Write-Host "   Possível problema: Agente ou Site não encontrado"
                        }
                    }
                    break
                }
            }
        }

        if (-not $success) {
            Write-Host "❌ Falha ao atualizar site. Nenhum método HTTP funcionou."
            Write-Host "   Métodos tentados: $($methods -join ', ')"
            Write-Host "   Isso pode indicar que a API não suporta atualização de sites via API"
            Write-Host "   ou que são necessárias permissões especiais."
            exit 1
        }
    }
} else {
    Write-Host "O site já está correto: ID $siteIdEsperado ($agentSiteAtual). Nenhuma alteração necessária."
}

Write-Host ""
Write-Host "Script executado com sucesso!"
Write-Host "Resumo Final:"
Write-Host "   - Hostname: $hostname"
Write-Host "   - IP Local: $ipLocal"
Write-Host "   - Site Atual: $agentSiteAtual (ID: $agentSiteId)"
Write-Host "   - Site Esperado: ID $siteIdEsperado"
Write-Host "   - Agente ID: $agentId"
Write-Host "   - Cliente: $clientName (ID: $clientId)"
Write-Host "   - Timestamp: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"

# Informações adicionais se verbose estiver ativo
Write-Verbose "=== INFORMAÇÕES DETALHADAS ==="
Write-Verbose "API URL: $apiUrl"
Write-Verbose "Sites configurados: $($sites.Count)"
foreach ($prefix in $sites.Keys) {
    Write-Verbose "  $prefix -> Site ID $($sites[$prefix])"
}
Write-Verbose "=============================="
